# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
env/
ENV/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
*.sqlite
*.db
test_*.sqlite
wallet_db.sqlite
test_wallet_db.sqlite

# Documentation
docs/_build/
# *.md  # Don't exclude README.md as it's needed by Poetry

# Development files
.env.local
.env.development
.env.test
docker-compose.override.yml

# Node modules (if any)
node_modules/

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
