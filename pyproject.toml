[tool.poetry]
name = "universal-wallet-platform"
version = "0.1.0"
description = "A fintech application for managing dual wallet systems (Fiat and Cryptocurrency)"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "^3.9"
fastapi = "^0.104.0"
uvicorn = "^0.23.2"
sqlmodel = "0.0.8"
alembic = "^1.12.0"
asyncpg = "^0.28.0"
aiosqlite = "^0.19.0"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-multipart = "^0.0.6"
pydantic = {extras = ["email", "dotenv"], version = "1.10.12"}
httpx = "^0.25.0"
tenacity = "^8.2.3"
python-dateutil = "^2.8.2"
loguru = "^0.7.2"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.2"
pytest-asyncio = "^0.21.1"
pytest-cov = "^4.1.0"
black = "^23.10.0"
isort = "^5.12.0"
mypy = "^1.6.1"
flake8 = "^6.1.0"
pre-commit = "^3.5.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ["py39"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
asyncio_mode = "auto"
