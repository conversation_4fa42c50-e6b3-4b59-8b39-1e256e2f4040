"""
Card model for user payment cards.
"""
from datetime import datetime
from enum import Enum
from typing import Optional, TYPE_CHECKING
import uuid

from sqlmodel import Field, Relationship, SQLModel

if TYPE_CHECKING:
    from app.models.user import User


class CardType(str, Enum):
    """Card types supported by the platform."""
    VISA = "visa"
    MASTERCARD = "mastercard"
    AMERICAN_EXPRESS = "american_express"
    DISCOVER = "discover"
    DINERS_CLUB = "diners_club"
    JCB = "jcb"


class CardStatus(str, Enum):
    """Card status options."""
    PENDING = "pending"          # Card request submitted, awaiting admin approval
    ACTIVE = "active"            # Card is active and can be used
    INACTIVE = "inactive"        # Card is temporarily deactivated
    EXPIRED = "expired"          # Card has expired
    CANCELLED = "cancelled"      # Card has been permanently cancelled


class Card(SQLModel, table=True):
    """User payment card model."""
    __tablename__ = "cards"

    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()), primary_key=True)
    user_id: str = Field(foreign_key="users.id", index=True)
    
    # Card details
    card_type: CardType
    card_number_last4: str = Field(max_length=4)  # Only store last 4 digits for security
    cardholder_name: str = Field(max_length=100)
    expiry_month: int = Field(ge=1, le=12)
    expiry_year: int = Field(ge=2024, le=2050)
    
    # Card status and management
    status: CardStatus = Field(default=CardStatus.PENDING)
    is_primary: bool = Field(default=False)  # Primary card for the user
    
    # Admin management fields
    requested_at: datetime = Field(default_factory=datetime.now)
    approved_at: Optional[datetime] = None
    approved_by: Optional[str] = Field(default=None, foreign_key="users.id")  # Admin who approved
    deactivated_at: Optional[datetime] = None
    deactivated_by: Optional[str] = Field(default=None, foreign_key="users.id")  # Admin who deactivated
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    
    # Relationships
    user: "User" = Relationship(back_populates="cards")
