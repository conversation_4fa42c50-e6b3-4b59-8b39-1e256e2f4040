"""
CRUD operations for cards.
"""
from datetime import datetime
from typing import List, Optional

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, and_

from app.crud.base import CRUDBase
from app.models.card import Card, CardStatus, CardType
from app.schemas.card import CardRequest, CardUpdate


class CRUDCard(CRUDBase[Card, CardRequest, CardUpdate]):
    """CRUD operations for cards."""
    
    async def create_card_request(
        self,
        db: AsyncSession,
        *,
        user_id: str,
        card_data: CardRequest
    ) -> Card:
        """
        Create a new card request.
        """
        db_obj = Card(
            user_id=user_id,
            card_type=card_data.card_type,
            card_number_last4=card_data.card_number_last4,
            cardholder_name=card_data.cardholder_name,
            expiry_month=card_data.expiry_month,
            expiry_year=card_data.expiry_year,
            status=CardStatus.PENDING
        )
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def get_by_user_id(
        self,
        db: AsyncSession,
        *,
        user_id: str,
        status: Optional[CardStatus] = None
    ) -> List[Card]:
        """
        Get all cards for a user, optionally filtered by status.
        """
        query = select(Card).where(Card.user_id == user_id)
        
        if status:
            query = query.where(Card.status == status)
        
        query = query.order_by(Card.created_at.desc())
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_by_status(
        self,
        db: AsyncSession,
        *,
        status: CardStatus,
        skip: int = 0,
        limit: int = 100
    ) -> List[Card]:
        """
        Get cards by status (for admin).
        """
        query = select(Card).where(Card.status == status)
        query = query.order_by(Card.requested_at.desc())
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_user_primary_card(
        self,
        db: AsyncSession,
        *,
        user_id: str
    ) -> Optional[Card]:
        """
        Get user's primary card.
        """
        query = select(Card).where(
            and_(
                Card.user_id == user_id,
                Card.is_primary == True,
                Card.status == CardStatus.ACTIVE
            )
        )
        result = await db.execute(query)
        return result.scalars().first()
    
    async def approve_card(
        self,
        db: AsyncSession,
        *,
        card_id: str,
        approved_by: str,
        set_as_primary: bool = False
    ) -> Optional[Card]:
        """
        Approve a card request.
        """
        card = await self.get(db, id=card_id)
        if not card or card.status != CardStatus.PENDING:
            return None
        
        # If setting as primary, remove primary status from other cards
        if set_as_primary:
            await self._remove_primary_status(db, user_id=card.user_id)
        
        card.status = CardStatus.ACTIVE
        card.approved_at = datetime.now()
        card.approved_by = approved_by
        card.is_primary = set_as_primary
        
        db.add(card)
        await db.commit()
        await db.refresh(card)
        return card
    
    async def reject_card(
        self,
        db: AsyncSession,
        *,
        card_id: str,
        rejected_by: str
    ) -> Optional[Card]:
        """
        Reject a card request.
        """
        card = await self.get(db, id=card_id)
        if not card or card.status != CardStatus.PENDING:
            return None
        
        card.status = CardStatus.CANCELLED
        card.deactivated_at = datetime.now()
        card.deactivated_by = rejected_by
        
        db.add(card)
        await db.commit()
        await db.refresh(card)
        return card
    
    async def update_card_status(
        self,
        db: AsyncSession,
        *,
        card_id: str,
        status: CardStatus,
        admin_id: str
    ) -> Optional[Card]:
        """
        Update card status (admin only).
        """
        card = await self.get(db, id=card_id)
        if not card:
            return None
        
        old_status = card.status
        card.status = status
        card.updated_at = datetime.now()
        
        if status in [CardStatus.INACTIVE, CardStatus.CANCELLED]:
            card.deactivated_at = datetime.now()
            card.deactivated_by = admin_id
            # Remove primary status if deactivating primary card
            if card.is_primary:
                card.is_primary = False
        elif status == CardStatus.ACTIVE and old_status in [CardStatus.INACTIVE]:
            card.deactivated_at = None
            card.deactivated_by = None
        
        db.add(card)
        await db.commit()
        await db.refresh(card)
        return card
    
    async def set_primary_card(
        self,
        db: AsyncSession,
        *,
        card_id: str,
        user_id: str
    ) -> Optional[Card]:
        """
        Set a card as primary for a user.
        """
        card = await self.get(db, id=card_id)
        if not card or card.user_id != user_id or card.status != CardStatus.ACTIVE:
            return None
        
        # Remove primary status from other cards
        await self._remove_primary_status(db, user_id=user_id)
        
        # Set this card as primary
        card.is_primary = True
        card.updated_at = datetime.now()
        
        db.add(card)
        await db.commit()
        await db.refresh(card)
        return card
    
    async def _remove_primary_status(
        self,
        db: AsyncSession,
        *,
        user_id: str
    ) -> None:
        """
        Remove primary status from all user's cards.
        """
        query = select(Card).where(
            and_(
                Card.user_id == user_id,
                Card.is_primary == True
            )
        )
        result = await db.execute(query)
        cards = result.scalars().all()
        
        for card in cards:
            card.is_primary = False
            db.add(card)
        
        await db.commit()
    
    async def get_user_card_summary(
        self,
        db: AsyncSession,
        *,
        user_id: str
    ) -> dict:
        """
        Get summary of user's cards.
        """
        cards = await self.get_by_user_id(db, user_id=user_id)
        
        total_cards = len(cards)
        active_cards = len([c for c in cards if c.status == CardStatus.ACTIVE])
        pending_cards = len([c for c in cards if c.status == CardStatus.PENDING])
        inactive_cards = len([c for c in cards if c.status == CardStatus.INACTIVE])
        
        primary_card = next((c for c in cards if c.is_primary), None)
        
        return {
            "total_cards": total_cards,
            "active_cards": active_cards,
            "pending_cards": pending_cards,
            "inactive_cards": inactive_cards,
            "primary_card": primary_card,
            "cards": cards
        }
    
    async def get_pending_cards_count(self, db: AsyncSession) -> int:
        """
        Get count of pending card requests for admin dashboard.
        """
        query = select(func.count(Card.id)).where(Card.status == CardStatus.PENDING)
        result = await db.execute(query)
        return result.scalar()


card = CRUDCard(Card)
