"""
Card schemas for API requests and responses.
"""
from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, Field, validator

from app.models.card import CardType, CardStatus


class CardBase(BaseModel):
    """Base card schema."""
    card_type: CardType
    cardholder_name: str = Field(..., min_length=2, max_length=100)
    expiry_month: int = Field(..., ge=1, le=12)
    expiry_year: int = Field(..., ge=2024, le=2050)


class CardRequest(CardBase):
    """Schema for requesting a new card."""
    card_number_last4: str = Field(..., min_length=4, max_length=4)

    @validator('card_number_last4')
    def validate_last4(cls, v):
        if not v.isdigit():
            raise ValueError('Last 4 digits must be numeric')
        return v


class CardUpdate(BaseModel):
    """Schema for updating card details (admin only)."""
    status: Optional[CardStatus] = None
    is_primary: Optional[bool] = None
    cardholder_name: Optional[str] = Field(None, min_length=2, max_length=100)
    expiry_month: Optional[int] = Field(None, ge=1, le=12)
    expiry_year: Optional[int] = Field(None, ge=2024, le=2050)


class CardResponse(CardBase):
    """Schema for card responses."""
    id: str
    user_id: str
    card_number_last4: str
    status: CardStatus
    is_primary: bool
    requested_at: datetime
    approved_at: Optional[datetime]
    approved_by: Optional[str]
    deactivated_at: Optional[datetime]
    deactivated_by: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class CardSummary(BaseModel):
    """Summary schema for user's cards."""
    total_cards: int
    active_cards: int
    pending_cards: int
    inactive_cards: int
    primary_card: Optional[CardResponse]
    cards: List[CardResponse]


class CardStatusUpdate(BaseModel):
    """Schema for updating card status (admin only)."""
    status: CardStatus
    reason: Optional[str] = Field(None, max_length=500)

    @validator('reason')
    def validate_reason_for_rejection(cls, v, values):
        if values.get('status') in [CardStatus.CANCELLED, CardStatus.INACTIVE] and not v:
            raise ValueError('Reason is required when deactivating or cancelling a card')
        return v


class CardApproval(BaseModel):
    """Schema for approving a card request."""
    approved: bool
    reason: Optional[str] = Field(None, max_length=500)
    set_as_primary: bool = Field(default=False)

    @validator('reason')
    def validate_reason_for_rejection(cls, v, values):
        if not values.get('approved') and not v:
            raise ValueError('Reason is required when rejecting a card request')
        return v
