"""
Card management endpoints.
"""
from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_active_user, get_current_admin_user
from app.crud.card import card as card_crud
from app.db.session import get_db
from app.models.user import User
from app.models.card import CardStatus
from app.schemas.card import (
    CardRequest,
    CardResponse,
    CardSummary,
    CardStatusUpdate,
    CardApproval,
    CardUpdate,
)

router = APIRouter()


# User endpoints
@router.post("/request", response_model=CardResponse)
async def request_card(
    card_request: CardRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Request a new payment card.
    """
    # Check if user already has a pending request
    pending_cards = await card_crud.get_by_user_id(
        db, user_id=current_user.id, status=CardStatus.PENDING
    )
    if pending_cards:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="You already have a pending card request. Please wait for admin approval."
        )
    
    # Create card request
    card = await card_crud.create_card_request(
        db, user_id=current_user.id, card_data=card_request
    )
    return card


@router.get("/me", response_model=CardSummary)
async def get_my_cards(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get current user's cards summary.
    """
    summary = await card_crud.get_user_card_summary(db, user_id=current_user.id)
    return summary


@router.get("/me/{card_id}", response_model=CardResponse)
async def get_my_card(
    card_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get a specific card by ID (user's own cards only).
    """
    card = await card_crud.get(db, id=card_id)
    if not card or card.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Card not found"
        )
    return card


@router.put("/me/{card_id}/set-primary", response_model=CardResponse)
async def set_primary_card(
    card_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Set a card as primary payment method.
    """
    card = await card_crud.set_primary_card(
        db, card_id=card_id, user_id=current_user.id
    )
    if not card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Card not found or cannot be set as primary"
        )
    return card


# Admin endpoints
@router.get("/admin/pending", response_model=List[CardResponse], tags=["admin"])
async def get_pending_card_requests(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get all pending card requests. Admin only.
    """
    cards = await card_crud.get_by_status(
        db, status=CardStatus.PENDING, skip=skip, limit=limit
    )
    return cards


@router.get("/admin/users/{user_id}", response_model=CardSummary, tags=["admin"])
async def get_user_cards(
    user_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get user's cards summary. Admin only.
    """
    summary = await card_crud.get_user_card_summary(db, user_id=user_id)
    return summary


@router.put("/admin/{card_id}/approve", response_model=CardResponse, tags=["admin"])
async def approve_card_request(
    card_id: str,
    approval: CardApproval,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Approve or reject a card request. Admin only.
    """
    if approval.approved:
        card = await card_crud.approve_card(
            db,
            card_id=card_id,
            approved_by=current_user.id,
            set_as_primary=approval.set_as_primary
        )
        if not card:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Card request not found or already processed"
            )
    else:
        card = await card_crud.reject_card(
            db, card_id=card_id, rejected_by=current_user.id
        )
        if not card:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Card request not found or already processed"
            )
    
    return card


@router.put("/admin/{card_id}/status", response_model=CardResponse, tags=["admin"])
async def update_card_status(
    card_id: str,
    status_update: CardStatusUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Update card status. Admin only.
    """
    card = await card_crud.update_card_status(
        db,
        card_id=card_id,
        status=status_update.status,
        admin_id=current_user.id
    )
    if not card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Card not found"
        )
    return card


@router.delete("/admin/{card_id}", tags=["admin"])
async def delete_card(
    card_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Delete a card permanently. Admin only.
    """
    card = await card_crud.get(db, id=card_id)
    if not card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Card not found"
        )
    
    await card_crud.remove(db, id=card_id)
    return {"message": "Card deleted successfully"}


@router.get("/admin/all", response_model=List[CardResponse], tags=["admin"])
async def get_all_cards(
    skip: int = 0,
    limit: int = 100,
    status: CardStatus = None,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get all cards with optional status filter. Admin only.
    """
    if status:
        cards = await card_crud.get_by_status(db, status=status, skip=skip, limit=limit)
    else:
        # Get all cards
        cards = await card_crud.get_multi(db, skip=skip, limit=limit)
    return cards
