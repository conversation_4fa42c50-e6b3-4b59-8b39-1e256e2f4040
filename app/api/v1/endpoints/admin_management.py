"""
Admin management endpoints for super admins.
"""
from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_user
from app.crud import user as user_crud
from app.db.session import get_db
from app.models.user import User, UserRole
from app.schemas.user import UserCreate, UserUpdate, User as UserSchema
from app.schemas.pagination import PaginatedResponse
from app.utils.pagination import paginate_users
from app.core.security import get_password_hash

router = APIRouter()


def get_current_super_admin_user(current_user: User = Depends(get_current_user)) -> User:
    """
    Dependency to ensure current user is a super admin.
    """
    if current_user.role != UserRole.SUPER_ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Super admin access required"
        )
    return current_user


def get_current_admin_or_super_admin_user(current_user: User = Depends(get_current_user)) -> User:
    """
    Dependency to ensure current user is admin or super admin.
    """
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin or super admin access required"
        )
    return current_user


# Super Admin only endpoints
@router.get("/admins", response_model=PaginatedResponse[UserSchema], tags=["super-admin"])
async def get_all_admins(
    request: Request,
    page: int = 1,
    page_size: int = 20,
    role: UserRole = None,
    current_user: User = Depends(get_current_super_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get all admin users with pagination. Super admin only.
    
    - **page**: Page number (default: 1)
    - **page_size**: Items per page (default: 20, max: 100)
    - **role**: Filter by role (admin, account_manager)
    """
    # Validate page_size
    if page_size > 100:
        page_size = 100
    
    # Filter for admin roles only
    if role and role not in [UserRole.ADMIN, UserRole.ACCOUNT_MANAGER]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Can only filter by admin or account_manager roles"
        )
    
    # If no role specified, show all admin roles
    if not role:
        # We'll need to modify pagination to handle multiple roles
        # For now, default to ADMIN
        role = UserRole.ADMIN
    
    # Build base URL for pagination
    base_url = str(request.url).split('?')[0]
    
    # Build query parameters for pagination links
    query_params = {"page_size": page_size}
    if role:
        query_params["role"] = role.value
    
    return await paginate_users(
        db=db,
        page=page,
        page_size=page_size,
        role_filter=role.value,
        base_url=base_url,
        query_params=query_params
    )


@router.post("/admins", response_model=UserSchema, tags=["super-admin"])
async def create_admin_user(
    admin_data: UserCreate,
    role: UserRole,
    current_user: User = Depends(get_current_super_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Create a new admin user. Super admin only.
    
    - **role**: Must be 'admin' or 'account_manager'
    """
    # Validate role
    if role not in [UserRole.ADMIN, UserRole.ACCOUNT_MANAGER]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Can only create admin or account_manager users"
        )
    
    # Check if user already exists
    existing_user = await user_crud.get_by_email(db, email=admin_data.email)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User with this email already exists"
        )
    
    # Create admin user
    user_in_data = admin_data.dict()
    user_in_data["role"] = role.value
    user_in_data["is_active"] = True  # Admin users are active by default
    
    user = await user_crud.create(db, obj_in=user_in_data)
    
    # Create wallets for the admin user
    from app.crud import fiat_wallet as fiat_wallet_crud
    from app.crud import crypto_wallet as crypto_wallet_crud
    from app.crud import wallet_config as wallet_config_crud
    
    # Create fiat wallet
    await fiat_wallet_crud.create(db, obj_in={
        "user_id": user.id,
        "balance": 0.0,
        "currency": "USD"
    })
    
    # Create crypto wallet with default address
    default_config = await wallet_config_crud.get_by_currency(db, currency="BTC")
    default_address = default_config.default_address if default_config else f"btc-{user.id[:16]}"
    
    await crypto_wallet_crud.create(db, obj_in={
        "user_id": user.id,
        "balance": 0.0,
        "currency": "BTC",
        "wallet_address": default_address
    })
    
    return user


@router.get("/admins/{admin_id}", response_model=UserSchema, tags=["super-admin"])
async def get_admin_user(
    admin_id: str,
    current_user: User = Depends(get_current_super_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get admin user by ID. Super admin only.
    """
    admin = await user_crud.get(db, id=admin_id)
    if not admin:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Admin user not found"
        )
    
    if admin.role not in [UserRole.ADMIN, UserRole.ACCOUNT_MANAGER]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User is not an admin"
        )
    
    return admin


@router.put("/admins/{admin_id}", response_model=UserSchema, tags=["super-admin"])
async def update_admin_user(
    admin_id: str,
    admin_update: UserUpdate,
    current_user: User = Depends(get_current_super_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Update admin user. Super admin only.
    """
    admin = await user_crud.get(db, id=admin_id)
    if not admin:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Admin user not found"
        )
    
    if admin.role not in [UserRole.ADMIN, UserRole.ACCOUNT_MANAGER]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User is not an admin"
        )
    
    # Prevent super admin from demoting themselves
    if admin.id == current_user.id and admin_update.role and admin_update.role != UserRole.SUPER_ADMIN:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot change your own role"
        )
    
    # Validate role changes
    if admin_update.role and admin_update.role not in [UserRole.ADMIN, UserRole.ACCOUNT_MANAGER, UserRole.SUPER_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid role for admin user"
        )
    
    admin = await user_crud.update(db, db_obj=admin, obj_in=admin_update)
    return admin


@router.delete("/admins/{admin_id}", tags=["super-admin"])
async def delete_admin_user(
    admin_id: str,
    current_user: User = Depends(get_current_super_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Delete admin user. Super admin only.
    """
    admin = await user_crud.get(db, id=admin_id)
    if not admin:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Admin user not found"
        )
    
    if admin.role not in [UserRole.ADMIN, UserRole.ACCOUNT_MANAGER]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User is not an admin"
        )
    
    # Prevent super admin from deleting themselves
    if admin.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete your own account"
        )
    
    await user_crud.remove(db, id=admin_id)
    return {"message": "Admin user deleted successfully"}


@router.put("/admins/{admin_id}/activate", response_model=UserSchema, tags=["super-admin"])
async def activate_admin_user(
    admin_id: str,
    current_user: User = Depends(get_current_super_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Activate admin user. Super admin only.
    """
    admin = await user_crud.get(db, id=admin_id)
    if not admin:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Admin user not found"
        )
    
    admin = await user_crud.update(db, db_obj=admin, obj_in={"is_active": True})
    return admin


@router.put("/admins/{admin_id}/deactivate", response_model=UserSchema, tags=["super-admin"])
async def deactivate_admin_user(
    admin_id: str,
    current_user: User = Depends(get_current_super_admin_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Deactivate admin user. Super admin only.
    """
    admin = await user_crud.get(db, id=admin_id)
    if not admin:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Admin user not found"
        )
    
    # Prevent super admin from deactivating themselves
    if admin.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot deactivate your own account"
        )
    
    admin = await user_crud.update(db, db_obj=admin, obj_in={"is_active": False})
    return admin
