import asyncio
import logging

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.core.config import settings
from app.core.security import get_password_hash
from app.db.session import async_session
from app.models.user import User, UserRole
from app.models.wallet import FiatWallet, CryptoWallet
import uuid

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def create_superuser() -> None:
    """
    Create a superuser if it doesn't exist.
    """
    if not settings.INITIAL_SUPERUSER_EMAIL or not settings.INITIAL_SUPERUSER_PASSWORD:
        logger.warning("Superuser email or password not set. Skipping creation.")
        return

    async with async_session() as session:
        # Check if superuser already exists
        result = await session.execute(
            select(User).where(User.email == settings.INITIAL_SUPERUSER_EMAIL)
        )
        user = result.scalars().first()

        if user:
            logger.info(f"Superuser {settings.INITIAL_SUPERUSER_EMAIL} already exists.")
            return

        # Create superuser
        hashed_password = get_password_hash(settings.INITIAL_SUPERUSER_PASSWORD)
        new_superuser = User(
            email=settings.INITIAL_SUPERUSER_EMAIL,
            hashed_password=hashed_password,
            first_name="Admin",
            last_name="User",
            is_active=True,
            role=UserRole.SUPER_ADMIN,
        )
        session.add(new_superuser)
        await session.commit()
        await session.refresh(new_superuser)

        # Create wallets for the superuser
        fiat_wallet = FiatWallet(user_id=new_superuser.id, balance=1000.0)
        crypto_wallet = CryptoWallet(
            user_id=new_superuser.id,
            balance=0.5,
            wallet_address=f"btc-{uuid.uuid4().hex[:16]}",
        )
        
        session.add(fiat_wallet)
        session.add(crypto_wallet)
        await session.commit()

        logger.info(f"Superuser {settings.INITIAL_SUPERUSER_EMAIL} created successfully.")


async def main() -> None:
    logger.info("Creating initial superuser...")
    await create_superuser()
    logger.info("Superuser creation process completed.")


if __name__ == "__main__":
    asyncio.run(main())
