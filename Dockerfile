FROM python:3.9-slim

WORKDIR /app/

# Install Poetry
RUN pip install poetry==1.6.1

# Configure poetry to not use a virtual environment
RUN poetry config virtualenvs.create false

# Copy poetry configuration files
COPY pyproject.toml README.md poetry.lock* /app/

# Copy application code first (needed for local package installation)
COPY . /app/

# Make scripts executable (do this before poetry install)
RUN chmod +x /app/scripts/start.sh
RUN chmod +x /app/scripts/prestart.sh

# Regenerate lock file and install dependencies (now that app code is available)
RUN poetry lock --no-update
RUN poetry install --only main --no-interaction --no-ansi

# Set environment variables
ENV PYTHONPATH=/app

# Expose the port
EXPOSE 8000

# Run the application directly
CMD ["sh", "-c", "alembic upgrade head && uvicorn app.main:app --host 0.0.0.0 --port 8000"]
